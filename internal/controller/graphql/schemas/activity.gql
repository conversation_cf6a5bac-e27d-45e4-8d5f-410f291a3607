# Activity System GraphQL Schema

# =====================================================
# Activity Tier Types
# =====================================================

type ActivityTier {
  id: Int!
  name: String!
  level: Int!
  minPoints: Int!
  maxPoints: Int
  color: String!
  icon: String
  description: String
  cashbackPercentage: Float!
  benefits: String
  createdAt: Time!
  updatedAt: Time!
}

type UserActivityLevel {
  id: ID!
  userId: ID!
  activityTier: ActivityTier!
  totalPoints: Int!
  currentPoints: Int!
  totalTradingVolume: Float!
  monthlyTradingVolume: Float!
  monthlyActiveDays: Int!
  totalCashbackEarned: Float!
  lastTierUpgrade: Time
  tierUpgradeCount: Int!
  previousTierId: Int
  createdAt: Time!
  updatedAt: Time!
}

type UserPointTransaction {
  id: ID!
  userId: ID!
  points: Int!
  type: String!
  source: String!
  description: String
  referenceId: ID
  referenceType: String
  metadata: String
  createdAt: Time!
}

type ActivityTierUpgrade {
  id: ID!
  userId: ID!
  fromTier: ActivityTier!
  toTier: ActivityTier!
  pointsAtUpgrade: Int!
  triggerType: String!
  triggerData: String
  createdAt: Time!
}

# =====================================================
# Activity Task Types
# =====================================================

enum TaskType {
  DAILY
  COMMUNITY
  TRADING
  ONE_TIME
  UNLIMITED
  PROGRESS
  MANUAL_UPDATE
}

enum TaskCategory {
  DAILY_TASKS
  COMMUNITY_TASKS
  TRADING_TASKS
}

enum TaskStatus {
  ACTIVE
  INACTIVE
  ARCHIVED
}

enum UserTaskStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  CLAIMED
  EXPIRED
}

type ActivityTask {
  id: ID!
  name: String!
  description: String
  type: TaskType!
  category: TaskCategory!
  status: TaskStatus!
  requiredCount: Int!
  requiredVolume: Float!
  minVolume: Float!
  timeWindowHours: Int
  validFrom: Time
  validUntil: Time
  pointReward: Int!
  cashbackReward: Float!
  config: String
  metadata: String
  icon: String
  color: String
  priority: Int!
  isHighlight: Boolean!
  createdAt: Time!
  updatedAt: Time!
}

type UserTaskProgress {
  id: ID!
  userId: ID!
  task: ActivityTask!
  status: UserTaskStatus!
  currentCount: Int!
  currentVolume: Float!
  completedCount: Int!
  currentStreak: Int!
  bestStreak: Int!
  lastStreakAt: Time
  startedAt: Time
  completedAt: Time
  claimedAt: Time
  expiresAt: Time
  lastUpdateAt: Time
  periodStart: Time
  periodEnd: Time
  pointsEarned: Int!
  cashbackEarned: Float!
  totalRewardUsd: Float!
  metadata: String
  createdAt: Time!
  updatedAt: Time!
}

type TaskCompletion {
  id: ID!
  userId: ID!
  task: ActivityTask!
  completedAt: Time!
  pointsAwarded: Int!
  cashbackAmount: Float!
  triggerType: String!
  triggerSource: String
  referenceId: ID
  referenceType: String
  completionData: String
  createdAt: Time!
}

# =====================================================
# Activity Reward Types
# =====================================================

enum ActivityRewardType {
  POINTS
  CASHBACK
  BONUS
  TIER_UPGRADE
}

enum ActivityRewardStatus {
  PENDING
  CLAIMED
  EXPIRED
}

type ActivityReward {
  id: ID!
  userId: ID!
  type: ActivityRewardType!
  status: ActivityRewardStatus!
  amount: Float!
  amountUsd: Float!
  currency: String!
  sourceType: String!
  sourceId: ID
  sourceName: String
  earnedAt: Time!
  claimedAt: Time
  expiresAt: Time
  description: String
  metadata: String
  createdAt: Time!
  updatedAt: Time!
}

type UserActivityStats {
  id: ID!
  userId: ID!
  dailyTasksCompleted: Int!
  dailyPointsEarned: Int!
  dailyCashbackEarned: Float!
  dailyTradingVolume: Float!
  dailyLastReset: Time!
  weeklyTasksCompleted: Int!
  weeklyPointsEarned: Int!
  weeklyCashbackEarned: Float!
  weeklyTradingVolume: Float!
  weeklyLastReset: Time!
  monthlyTasksCompleted: Int!
  monthlyPointsEarned: Int!
  monthlyCashbackEarned: Float!
  monthlyTradingVolume: Float!
  monthlyActiveDays: Int!
  monthlyLastReset: Time!
  totalTasksCompleted: Int!
  totalPointsEarned: Int!
  totalCashbackEarned: Float!
  totalTradingVolume: Float!
  totalActiveDays: Int!
  currentLoginStreak: Int!
  bestLoginStreak: Int!
  currentTradingStreak: Int!
  bestTradingStreak: Int!
  lastActivityAt: Time
  lastTradingAt: Time
  createdAt: Time!
  updatedAt: Time!
}

# =====================================================
# Input Types
# =====================================================

input GetUserTasksInput {
  category: TaskCategory
  status: UserTaskStatus
}

input ClaimTaskRewardInput {
  taskId: ID!
}

input RefreshUserTasksInput {
  force: Boolean = false
}

input CompleteTaskInput {
  taskId: ID!
  triggerType: String!
  triggerSource: String
  referenceId: ID
}

# =====================================================
# Response Types
# =====================================================

type ActivityTierResponse {
  tiers: [ActivityTier!]!
  success: Boolean!
  message: String
}

type UserActivityLevelResponse {
  userLevel: UserActivityLevel
  success: Boolean!
  message: String
}

type UserTasksResponse {
  tasks: [UserTaskProgress!]!
  success: Boolean!
  message: String
}

type TaskRewardClaimResponse {
  success: Boolean!
  message: String!
  pointsAwarded: Int
  cashbackAwarded: Float
}

type UserActivityStatsResponse {
  stats: UserActivityStats
  success: Boolean!
  message: String
}

type ActivityRewardsResponse {
  rewards: [ActivityReward!]!
  totalPending: Float!
  success: Boolean!
  message: String
}
