package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.78

import (
	"context"
	"fmt"
	"time"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
)

// AdminCreateActivityTier is the resolver for the adminCreateActivityTier field.
func (r *mutationResolver) AdminCreateActivityTier(ctx context.Context, input gql_model.CreateActivityTierInput) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminCreateActivityTier - adminCreateActivityTier"))
}

// AdminUpdateActivityTier is the resolver for the adminUpdateActivityTier field.
func (r *mutationResolver) AdminUpdateActivityTier(ctx context.Context, input gql_model.UpdateActivityTierInput) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminUpdateActivityTier - adminUpdateActivityTier"))
}

// AdminDeleteActivityTier is the resolver for the adminDeleteActivityTier field.
func (r *mutationResolver) AdminDeleteActivityTier(ctx context.Context, id int) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminDeleteActivityTier - adminDeleteActivityTier"))
}

// AdminCreateActivityTask is the resolver for the adminCreateActivityTask field.
func (r *mutationResolver) AdminCreateActivityTask(ctx context.Context, input gql_model.CreateActivityTaskInput) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminCreateActivityTask - adminCreateActivityTask"))
}

// AdminUpdateActivityTask is the resolver for the adminUpdateActivityTask field.
func (r *mutationResolver) AdminUpdateActivityTask(ctx context.Context, input gql_model.UpdateActivityTaskInput) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminUpdateActivityTask - adminUpdateActivityTask"))
}

// AdminDeleteActivityTask is the resolver for the adminDeleteActivityTask field.
func (r *mutationResolver) AdminDeleteActivityTask(ctx context.Context, id string) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminDeleteActivityTask - adminDeleteActivityTask"))
}

// AdminBulkTaskOperation is the resolver for the adminBulkTaskOperation field.
func (r *mutationResolver) AdminBulkTaskOperation(ctx context.Context, input gql_model.BulkTaskOperationInput) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminBulkTaskOperation - adminBulkTaskOperation"))
}

// AdminAdjustUserPoints is the resolver for the adminAdjustUserPoints field.
func (r *mutationResolver) AdminAdjustUserPoints(ctx context.Context, input gql_model.ManualPointAdjustmentInput) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminAdjustUserPoints - adminAdjustUserPoints"))
}

// AdminResetUserProgress is the resolver for the adminResetUserProgress field.
func (r *mutationResolver) AdminResetUserProgress(ctx context.Context, userID string, taskID string) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminResetUserProgress - adminResetUserProgress"))
}

// AdminForceUserTierUpgrade is the resolver for the adminForceUserTierUpgrade field.
func (r *mutationResolver) AdminForceUserTierUpgrade(ctx context.Context, userID string, tierLevel int) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminForceUserTierUpgrade - adminForceUserTierUpgrade"))
}

// AdminResetDailyTasks is the resolver for the adminResetDailyTasks field.
func (r *mutationResolver) AdminResetDailyTasks(ctx context.Context) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminResetDailyTasks - adminResetDailyTasks"))
}

// AdminProcessExpiredTasks is the resolver for the adminProcessExpiredTasks field.
func (r *mutationResolver) AdminProcessExpiredTasks(ctx context.Context) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminProcessExpiredTasks - adminProcessExpiredTasks"))
}

// AdminRecalculateUserTiers is the resolver for the adminRecalculateUserTiers field.
func (r *mutationResolver) AdminRecalculateUserTiers(ctx context.Context) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminRecalculateUserTiers - adminRecalculateUserTiers"))
}

// AdminInitializeUserActivity is the resolver for the adminInitializeUserActivity field.
func (r *mutationResolver) AdminInitializeUserActivity(ctx context.Context, userID string) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminInitializeUserActivity - adminInitializeUserActivity"))
}

// AdminBulkInitializeUsers is the resolver for the adminBulkInitializeUsers field.
func (r *mutationResolver) AdminBulkInitializeUsers(ctx context.Context, userIds []string) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminBulkInitializeUsers - adminBulkInitializeUsers"))
}

// AdminBulkResetUserProgress is the resolver for the adminBulkResetUserProgress field.
func (r *mutationResolver) AdminBulkResetUserProgress(ctx context.Context, userIds []string, taskIds []string) (*gql_model.AdminOperationResult, error) {
	panic(fmt.Errorf("not implemented: AdminBulkResetUserProgress - adminBulkResetUserProgress"))
}

// AdminActivityDashboard is the resolver for the adminActivityDashboard field.
func (r *queryResolver) AdminActivityDashboard(ctx context.Context) (*gql_model.AdminActivityDashboard, error) {
	panic(fmt.Errorf("not implemented: AdminActivityDashboard - adminActivityDashboard"))
}

// AdminActivityTiers is the resolver for the adminActivityTiers field.
func (r *queryResolver) AdminActivityTiers(ctx context.Context) ([]*gql_model.ActivityTier, error) {
	panic(fmt.Errorf("not implemented: AdminActivityTiers - adminActivityTiers"))
}

// AdminActivityTier is the resolver for the adminActivityTier field.
func (r *queryResolver) AdminActivityTier(ctx context.Context, id int) (*gql_model.ActivityTier, error) {
	panic(fmt.Errorf("not implemented: AdminActivityTier - adminActivityTier"))
}

// AdminActivityTasks is the resolver for the adminActivityTasks field.
func (r *queryResolver) AdminActivityTasks(ctx context.Context, filter *gql_model.AdminTaskFilterInput) (*gql_model.AdminTaskListResponse, error) {
	panic(fmt.Errorf("not implemented: AdminActivityTasks - adminActivityTasks"))
}

// AdminActivityTask is the resolver for the adminActivityTask field.
func (r *queryResolver) AdminActivityTask(ctx context.Context, id string) (*gql_model.ActivityTask, error) {
	panic(fmt.Errorf("not implemented: AdminActivityTask - adminActivityTask"))
}

// AdminTaskAnalytics is the resolver for the adminTaskAnalytics field.
func (r *queryResolver) AdminTaskAnalytics(ctx context.Context, taskID string) (*gql_model.AdminTaskAnalytics, error) {
	panic(fmt.Errorf("not implemented: AdminTaskAnalytics - adminTaskAnalytics"))
}

// AdminUserActivities is the resolver for the adminUserActivities field.
func (r *queryResolver) AdminUserActivities(ctx context.Context, filter *gql_model.AdminUserFilterInput) (*gql_model.AdminUserListResponse, error) {
	panic(fmt.Errorf("not implemented: AdminUserActivities - adminUserActivities"))
}

// AdminUserActivityDetail is the resolver for the adminUserActivityDetail field.
func (r *queryResolver) AdminUserActivityDetail(ctx context.Context, userID string) (*gql_model.AdminUserActivityDetail, error) {
	panic(fmt.Errorf("not implemented: AdminUserActivityDetail - adminUserActivityDetail"))
}

// AdminActivityStats is the resolver for the adminActivityStats field.
func (r *queryResolver) AdminActivityStats(ctx context.Context, dateFrom time.Time, dateTo time.Time) (*gql_model.AdminActivityDashboard, error) {
	panic(fmt.Errorf("not implemented: AdminActivityStats - adminActivityStats"))
}

// AdminSystemHealth is the resolver for the adminSystemHealth field.
func (r *queryResolver) AdminSystemHealth(ctx context.Context) (*gql_model.SystemHealth, error) {
	panic(fmt.Errorf("not implemented: AdminSystemHealth - adminSystemHealth"))
}

// AdminExportUserActivities is the resolver for the adminExportUserActivities field.
func (r *queryResolver) AdminExportUserActivities(ctx context.Context, filter *gql_model.AdminUserFilterInput) (string, error) {
	panic(fmt.Errorf("not implemented: AdminExportUserActivities - adminExportUserActivities"))
}

// AdminExportTaskCompletions is the resolver for the adminExportTaskCompletions field.
func (r *queryResolver) AdminExportTaskCompletions(ctx context.Context, filter *gql_model.AdminTaskFilterInput) (string, error) {
	panic(fmt.Errorf("not implemented: AdminExportTaskCompletions - adminExportTaskCompletions"))
}
